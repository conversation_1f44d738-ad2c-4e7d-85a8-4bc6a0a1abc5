# Guide de Test - Fonctionnalités de la Sidebar

## ✅ Fonctionnalités Implémentées (v3)

Les fonctionnalités complètes de création et gestion des projets, personas et artifacts sont maintenant opérationnelles !

### Nouvelles Fonctionnalités Implémentées

1. **Système de stockage complet** - Stockage local avec validation, backup et gestion de quota
2. **Création de projets** - Modal avec formulaire complet pour créer de nouveaux projets
3. **Création de personas** - Modal avec formulaire pour créer et configurer des personas
4. **Activation de personas** - Clic sur une persona pour l'activer et l'afficher
5. **Détails de projets** - Clic sur un projet pour voir ses détails complets
6. **Notifications** - Système de notifications pour les actions réussies/échouées
7. **Modals interactifs** - Interface utilisateur complète avec modals et formulaires
8. **Persistance des données** - Toutes les données sont sauvegardées localement

## Comment Tester

### 1. Test de Debug (Recommandé pour diagnostic)
Ouvrez le fichier de debug dans votre navigateur :
```
file:///Users/<USER>/projects/gemini-extension/extension/debug-sidebar.html
```
Ce fichier simule le contexte de l'extension et affiche les logs de debug.

### 2. Test Standalone (Fonctionnel)
Ouvrez le fichier de test dans votre navigateur :
```
file:///Users/<USER>/projects/gemini-extension/extension/test-sidebar.html
```

### 3. Test dans l'Extension Chrome (Principal)

1. **Rechargez l'extension** :
   - Allez dans `chrome://extensions/`
   - Trouvez "Coder Companion"
   - Cliquez sur le bouton de rechargement 🔄
   - Ou utilisez le script : `./reload-extension.sh`

2. **Testez sur Google AI Studio** :
   - Allez sur `https://aistudio.google.com/`
   - Cliquez sur le bouton de l'extension (icône hamburger en haut à droite)
   - Ou utilisez le raccourci `Ctrl+Shift+C`
   - Ouvrez la console développeur (F12) pour voir les logs

## Fonctionnalités Testables

### ✅ Projects Tab - FONCTIONNALITÉS COMPLÈTES
- **Navigation** : Clic sur l'onglet "Projects" affiche la liste des projets
- **Création** : Bouton "New Project" ouvre un modal avec formulaire complet
- **Détails** : Clic sur un projet affiche ses détails dans un modal
- **Formulaire** : Nom, description, priorité, tags avec validation
- **Persistance** : Les projets créés sont sauvegardés et persistent
- **Statistiques** : Affiche le total, projets actifs et complétés (mis à jour en temps réel)

### ✅ Personas Tab - FONCTIONNALITÉS COMPLÈTES
- **Navigation** : Clic sur l'onglet "Personas" affiche la liste des personas
- **Création** : Bouton "New Persona" ouvre un modal avec formulaire avancé
- **Activation** : Clic sur un persona l'active et l'affiche en haut
- **Formulaire** : Nom, rôle, personnalité, expertise, prompt système
- **Persona Active** : Affichage de la persona actuellement active
- **Persistance** : Les personas créés sont sauvegardés
- **Statistiques** : Affiche le total, personas actifs et usage

### ✅ Artifacts Tab
- **Navigation** : Clic sur l'onglet "Artifacts" affiche la liste des artifacts
- **Données** : Affiche 2 artifacts mockés avec nombre de mots
- **Interactions** : Clic sur un artifact affiche une alerte
- **Boutons** : "New Artifact" et "Refresh" fonctionnent
- **Statistiques** : Affiche le total, mots et versions

### ✅ Export Tab
- **Navigation** : Clic sur l'onglet "Export" affiche les options d'export
- **Données** : Affiche 4 options d'export différentes
- **Interactions** : Clic sur une option affiche une alerte
- **Boutons** : Tous les boutons d'export fonctionnent

## Éléments Visuels

### États de Chargement
- **Spinner animé** pendant le chargement des données
- **Message de chargement** approprié pour chaque onglet

### Styles
- **Hover effects** sur les éléments de liste
- **Indicateurs de statut** colorés (active, draft, completed, ready)
- **Statistiques** dans le footer de chaque panel
- **Icons SVG** pour tous les boutons et onglets

## 🎯 Nouvelles Fonctionnalités à Tester

### Création de Projets
1. Cliquez sur "New Project" dans l'onglet Projects
2. Remplissez le formulaire (nom requis)
3. Ajoutez des tags séparés par des virgules
4. Cliquez "Create Project"
5. ✅ Vérifiez que le projet apparaît dans la liste
6. ✅ Vérifiez que les statistiques sont mises à jour

### Création de Personas
1. Cliquez sur "New Persona" dans l'onglet Personas
2. Remplissez le formulaire complet
3. Ajoutez des compétences séparées par des virgules
4. Cliquez "Create Persona"
5. ✅ Vérifiez que le persona apparaît dans la liste

### Activation de Personas
1. Cliquez sur un persona dans la liste
2. ✅ Vérifiez qu'il apparaît dans la section "Active Persona"
3. ✅ Vérifiez la notification de succès
4. Cliquez "Clear" pour désactiver

### Détails de Projets
1. Cliquez sur un projet dans la liste
2. ✅ Vérifiez que le modal s'ouvre avec tous les détails
3. ✅ Vérifiez l'affichage des tags, statut, priorité

## Prochaines Étapes

Les fonctionnalités principales sont implémentées ! Il reste à faire :

1. **Implémenter la création d'artifacts** avec éditeur de contenu
2. **Ajouter l'édition** des projets et personas existants
3. **Implémenter l'export réel** des fichiers (JSON, Markdown, etc.)
4. **Connecter avec l'API Google AI Studio** pour utiliser les personas
5. **Ajouter la suppression** des éléments avec confirmation

## Dépannage

Si les panels ne se chargent pas :
1. Vérifiez la console du navigateur pour les erreurs
2. Assurez-vous que tous les fichiers de components existent
3. Rechargez l'extension Chrome
4. Testez d'abord avec le fichier standalone

## Logs de Debug

Les actions sont loggées dans la console :
- `[Sidebar] Switching to tab: [tabname]`
- `[Sidebar] Clicked [tab] item: [id]`
- `[Sidebar] [Action] button clicked`
