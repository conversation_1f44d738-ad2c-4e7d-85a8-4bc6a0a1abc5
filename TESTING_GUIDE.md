# Guide de Test - Fonctionnalités de la Sidebar

## ✅ Corrections Apportées (v2)

Les fonctionnalités des onglets Projects, Personas, Artifacts et Export ont été corrigées et sont maintenant opérationnelles dans l'extension Chrome.

### Problèmes Résolus

1. **Chargement des panels dans l'extension** - Les panels sont maintenant créés directement en JavaScript au lieu d'être chargés via fetch
2. **Contexte d'exécution** - Résolution du problème de contexte entre la page web et l'extension
3. **Gestion des événements** - Les clics sur les éléments déclenchent maintenant des actions
4. **Données mockées** - Ajout de données de démonstration pour tous les onglets
5. **Styles CSS** - Ajout des styles manquants pour les listes et éléments de chargement
6. **Statistiques** - Affichage des statistiques dans le footer de chaque panel

## Comment Tester

### 1. Test de Debug (Recommandé pour diagnostic)
Ouvrez le fichier de debug dans votre navigateur :
```
file:///Users/<USER>/projects/gemini-extension/extension/debug-sidebar.html
```
Ce fichier simule le contexte de l'extension et affiche les logs de debug.

### 2. Test Standalone (Fonctionnel)
Ouvrez le fichier de test dans votre navigateur :
```
file:///Users/<USER>/projects/gemini-extension/extension/test-sidebar.html
```

### 3. Test dans l'Extension Chrome (Principal)

1. **Rechargez l'extension** :
   - Allez dans `chrome://extensions/`
   - Trouvez "Coder Companion"
   - Cliquez sur le bouton de rechargement 🔄
   - Ou utilisez le script : `./reload-extension.sh`

2. **Testez sur Google AI Studio** :
   - Allez sur `https://aistudio.google.com/`
   - Cliquez sur le bouton de l'extension (icône hamburger en haut à droite)
   - Ou utilisez le raccourci `Ctrl+Shift+C`
   - Ouvrez la console développeur (F12) pour voir les logs

## Fonctionnalités Testables

### ✅ Projects Tab
- **Navigation** : Clic sur l'onglet "Projects" affiche la liste des projets
- **Données** : Affiche 2 projets mockés avec statuts et métadonnées
- **Interactions** : Clic sur un projet affiche une alerte (fonctionnalité à implémenter)
- **Boutons** : "New Project" et "Refresh" fonctionnent
- **Statistiques** : Affiche le total, projets actifs et complétés

### ✅ Personas Tab
- **Navigation** : Clic sur l'onglet "Personas" affiche la liste des personas
- **Données** : Affiche 2 personas mockés
- **Interactions** : Clic sur un persona affiche une alerte
- **Boutons** : "New Persona" et "Refresh" fonctionnent
- **Statistiques** : Affiche le total, personas actifs et usage

### ✅ Artifacts Tab
- **Navigation** : Clic sur l'onglet "Artifacts" affiche la liste des artifacts
- **Données** : Affiche 2 artifacts mockés avec nombre de mots
- **Interactions** : Clic sur un artifact affiche une alerte
- **Boutons** : "New Artifact" et "Refresh" fonctionnent
- **Statistiques** : Affiche le total, mots et versions

### ✅ Export Tab
- **Navigation** : Clic sur l'onglet "Export" affiche les options d'export
- **Données** : Affiche 4 options d'export différentes
- **Interactions** : Clic sur une option affiche une alerte
- **Boutons** : Tous les boutons d'export fonctionnent

## Éléments Visuels

### États de Chargement
- **Spinner animé** pendant le chargement des données
- **Message de chargement** approprié pour chaque onglet

### Styles
- **Hover effects** sur les éléments de liste
- **Indicateurs de statut** colorés (active, draft, completed, ready)
- **Statistiques** dans le footer de chaque panel
- **Icons SVG** pour tous les boutons et onglets

## Prochaines Étapes

Les fonctionnalités de base sont maintenant opérationnelles. Pour une implémentation complète, il faudra :

1. **Remplacer les données mockées** par de vraies données stockées
2. **Implémenter les actions réelles** (création, édition, suppression)
3. **Ajouter la persistance** des données
4. **Connecter avec l'API Google AI Studio**
5. **Implémenter l'export réel** des fichiers

## Dépannage

Si les panels ne se chargent pas :
1. Vérifiez la console du navigateur pour les erreurs
2. Assurez-vous que tous les fichiers de components existent
3. Rechargez l'extension Chrome
4. Testez d'abord avec le fichier standalone

## Logs de Debug

Les actions sont loggées dans la console :
- `[Sidebar] Switching to tab: [tabname]`
- `[Sidebar] Clicked [tab] item: [id]`
- `[Sidebar] [Action] button clicked`
