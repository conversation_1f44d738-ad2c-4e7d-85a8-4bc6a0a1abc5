// content-script.js - Main injection script for Google AI Studio integration
class CoderCompanionInjector {
  constructor() {
    this.sidebarContainer = null;
    this.toggleButton = null;
    this.isInjected = false;
    this.isSidebarOpen = false;
  }

  async init() {
    console.log('[Coder Companion] Initializing extension...');

    try {
      await this.waitForPageLoad();
      this.injectToggleButton();
      this.injectSidebar();
      this.setupEventListeners();
      this.loadExtensionState();

      // Mark as initialized and set up message listener
      this.isInitialized = true;
      this.setupMessageListener();

      console.log('[Coder Companion] Extension initialized successfully');
    } catch (error) {
      console.error('[Coder Companion] Initialization failed:', error);
    }
  }

  waitForPageLoad() {
    return new Promise((resolve) => {
      if (document.readyState === 'complete') {
        resolve();
      } else {
        window.addEventListener('load', resolve);
      }
    });
  }

  injectToggleButton() {
    console.log('[Coder Companion] Injecting toggle button...');

    this.toggleButton = document.createElement('button');
    this.toggleButton.id = 'cc-toggle-btn';
    // Remove Tailwind classes - CSS is handled by extension.css
    this.toggleButton.innerHTML = `
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M3 12h18M3 6h18M3 18h18"/>
      </svg>
    `;
    this.toggleButton.title = 'Toggle Coder Companion Sidebar';

    document.body.appendChild(this.toggleButton);
  }

  injectSidebar() {
    console.log('[Coder Companion] Injecting sidebar...');

    this.sidebarContainer = document.createElement('div');
    this.sidebarContainer.id = 'cc-sidebar';
    // Remove Tailwind classes - CSS is handled by extension.css

    // Load sidebar HTML from extension resources
    fetch(chrome.runtime.getURL('sidebar/index.html'))
      .then(response => response.text())
      .then(html => {
        console.log('[Coder Companion] Sidebar HTML loaded, length:', html.length);
        this.sidebarContainer.innerHTML = html;
        document.body.appendChild(this.sidebarContainer);
        console.log('[Coder Companion] Sidebar added to DOM');
        this.initializeSidebarComponents();
        this.isInjected = true;
        console.log('[Coder Companion] Sidebar initialization complete');
      })
      .catch(error => {
        console.error('[Coder Companion] Failed to load sidebar:', error);
      });
  }


  setupEventListeners() {
    console.log('[Coder Companion] Setting up event listeners...');

    // Toggle button click
    this.toggleButton.addEventListener('click', () => {
      this.toggleSidebar();
    });

    // Keyboard shortcut (Ctrl+Shift+C)
    document.addEventListener('keydown', (event) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'C') {
        event.preventDefault();
        this.toggleSidebar();
      }
    });

    // Close sidebar when clicking outside
    document.addEventListener('click', (event) => {
      if (this.isSidebarOpen &&
          !this.sidebarContainer.contains(event.target) &&
          !this.toggleButton.contains(event.target)) {
        this.closeSidebar();
      }
    });
  }

  toggleSidebar() {
    if (this.isSidebarOpen) {
      this.closeSidebar();
    } else {
      this.openSidebar();
    }
  }

  openSidebar() {
    if (this.sidebarContainer && !this.isSidebarOpen) {
      console.log('[Coder Companion] Opening sidebar - current classes:', this.sidebarContainer.className);

      // Use CSS class instead of direct style manipulation
      this.sidebarContainer.classList.add('open');
      this.isSidebarOpen = true;

      console.log('[Coder Companion] Sidebar opened - new classes:', this.sidebarContainer.className);

      // Debug: Check sidebar position and visibility
      const rect = this.sidebarContainer.getBoundingClientRect();
      const computed = window.getComputedStyle(this.sidebarContainer);
      console.log('[Coder Companion] Sidebar position:', {
        top: rect.top,
        left: rect.left,
        width: rect.width,
        height: rect.height,
        display: computed.display,
        visibility: computed.visibility,
        opacity: computed.opacity,
        zIndex: computed.zIndex,
        transform: computed.transform
      });

      // Update toggle button appearance - remove Tailwind classes and use a simple active state
      this.toggleButton.style.background = 'linear-gradient(135deg, #2563eb, #1e40af)';
      this.toggleButton.innerHTML = `
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M18 6L6 18M6 6l12 12"/>
        </svg>
      `;

      // Notify sidebar components
      this.dispatchSidebarEvent('sidebarOpened');
    }
  }

  closeSidebar() {
    if (this.sidebarContainer && this.isSidebarOpen) {
      // Use CSS class instead of direct style manipulation
      this.sidebarContainer.classList.remove('open');
      this.isSidebarOpen = false;

      // Update toggle button appearance - reset to default gradient
      this.toggleButton.style.background = 'linear-gradient(135deg, #3b82f6, #1d4ed8)';
      this.toggleButton.innerHTML = `
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="pointer-events: none;">
          <path d="M3 12h18M3 6h18M3 18h18" style="pointer-events: none;"/>
        </svg>
      `;

      // Notify sidebar components
      this.dispatchSidebarEvent('sidebarClosed');
    }
  }

  dispatchSidebarEvent(eventType) {
    const event = new CustomEvent(eventType, {
      detail: { sidebar: this.sidebarContainer }
    });
    document.dispatchEvent(event);
  }

  loadExtensionState() {
    console.log('[Coder Companion] Loading extension state...');

    // Load saved preferences and state from storage
    chrome.storage.local.get(['sidebarOpen', 'lastActiveTab'], (result) => {
      if (result.sidebarOpen) {
        // Small delay to ensure DOM is ready, then open sidebar
        setTimeout(() => {
          this.openSidebar();
        }, 100);
      }
    });
  }

  initializeSidebarComponents() {
    console.log('[Coder Companion] Initializing sidebar components...');

    // Wait for sidebar content to be fully loaded
    const checkSidebarReady = () => {
      const sidebarDocument = this.sidebarContainer.contentDocument;
      if (sidebarDocument && sidebarDocument.readyState === 'complete') {
        // Sidebar is ready, we can now interact with it
        this.setupSidebarCommunication();
      } else {
        // Check again in a moment
        setTimeout(checkSidebarReady, 50);
      }
    };

    checkSidebarReady();
  }

  setupMessageListener() {
    console.log('[Coder Companion] Setting up message listener...');

    // Listen for messages from the popup
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      console.log('[Coder Companion] Received message:', message);

      if (message.type === 'PING') {
        console.log('[Coder Companion] Received PING, responding with PONG');
        const response = { type: 'PONG', isReady: this.isInitialized && this.isInjected };
        console.log('[Coder Companion] PING response:', response);
        sendResponse(response);
        return true;
      }

      if (message.type === 'OPEN_SIDEBAR') {
        console.log('[Coder Companion] Received OPEN_SIDEBAR message');
        console.log('[Coder Companion] Extension state:', {
          isInitialized: this.isInitialized,
          isInjected: this.isInjected,
          sidebarContainer: !!this.sidebarContainer,
          isSidebarOpen: this.isSidebarOpen
        });

        // Ensure extension is fully initialized before opening sidebar
        if (!this.isInitialized || !this.isInjected) {
          console.warn('[Coder Companion] Extension not fully initialized, waiting...');
          // Wait for initialization to complete
          const checkReady = () => {
            if (this.isInitialized && this.isInjected) {
              try {
                this.openSidebar();
                console.log('[Coder Companion] Sidebar opened successfully');
                const response = { success: true };
                console.log('[Coder Companion] OPEN_SIDEBAR response:', response);
                sendResponse(response);
              } catch (error) {
                console.error('[Coder Companion] Failed to open sidebar:', error);
                const response = { success: false, error: error.message };
                console.log('[Coder Companion] OPEN_SIDEBAR error response:', response);
                sendResponse(response);
              }
            } else {
              setTimeout(checkReady, 100);
            }
          };
          checkReady();
        } else {
          try {
            this.openSidebar();
            console.log('[Coder Companion] Sidebar opened successfully');
            const response = { success: true };
            console.log('[Coder Companion] OPEN_SIDEBAR response:', response);
            sendResponse(response);
          } catch (error) {
            console.error('[Coder Companion] Failed to open sidebar:', error);
            const response = { success: false, error: error.message };
            console.log('[Coder Companion] OPEN_SIDEBAR error response:', response);
            sendResponse(response);
          }
        }
      }
      return true; // Keep the message channel open for async response
    });

    console.log('[Coder Companion] Message listener set up successfully');
  }

  setupSidebarCommunication() {
    console.log('[Coder Companion] Setting up sidebar communication...');

    // Listen for messages from the sidebar
    const sidebarWindow = this.sidebarContainer.contentWindow;

    if (sidebarWindow) {
      // Handle sidebar close events
      sidebarWindow.addEventListener('message', (event) => {
        if (event.data.type === 'SIDEBAR_CLOSE') {
          this.closeSidebar();
        }
      });

      // Send initialization message to sidebar
      sidebarWindow.postMessage({
        type: 'SIDEBAR_INIT',
        data: {
          url: window.location.href,
          timestamp: Date.now()
        }
      }, '*');
    }
  }

  saveExtensionState() {
    const state = {
      sidebarOpen: this.isSidebarOpen,
      lastActiveTab: 'projects' // Default tab
    };

    chrome.storage.local.set(state);
  }
}

// Initialize when script loads
console.log('[Coder Companion] Content script loaded');
new CoderCompanionInjector().init();

// Save state on page unload
window.addEventListener('beforeunload', () => {
  // Note: This is a placeholder - actual implementation will be in the injector instance
  console.log('[Coder Companion] Saving extension state...');
});