/* extension/sidebar/style.css - Sidebar-specific styles */

/* Import main extension styles */
@import url('../styles/extension.css');

/* Sidebar-specific overrides and additions */
.sidebar-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* Tab Header */
.tab-header {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
  padding: 0 4px;
}

.tab-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.search-container {
  position: relative;
}

.search-container svg {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  color: #6b7280;
  pointer-events: none;
}

.search-container input {
  padding-left: 36px !important;
}

/* Empty states */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.empty-state svg {
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* Project List Items */
.project-item,
.persona-item,
.artifact-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  transition: background-color 0.2s ease;
}

.project-item:hover,
.persona-item:hover,
.artifact-item:hover {
  background-color: #f9fafb !important;
}

.project-title,
.persona-title,
.artifact-title {
  font-weight: 600;
  color: #111827;
  font-size: 14px;
}

.project-meta,
.persona-meta,
.artifact-meta {
  font-size: 12px;
  color: #6b7280;
  display: flex;
  align-items: center;
  gap: 12px;
}

.project-description,
.persona-description,
.artifact-description {
  font-size: 13px;
  color: #4b5563;
  line-height: 1.4;
  margin-top: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Export sections */
.export-section {
  margin-bottom: 32px;
}

.export-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.export-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.export-actions .cc-button {
  justify-content: flex-start;
  width: 100%;
}

/* Export history items */
.export-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
}

.export-item-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.export-item-title {
  font-weight: 500;
  color: #111827;
  font-size: 14px;
}

.export-item-meta {
  font-size: 12px;
  color: #6b7280;
}

.export-item-actions {
  display: flex;
  gap: 8px;
}

.export-item-actions button {
  padding: 6px 8px;
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.export-item-actions button:hover {
  background: #e5e7eb;
  color: #374151;
}

/* Modal styles */
.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10001;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.modal {
  background: white;
  border-radius: 8px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.modal-close {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #f3f4f6;
  color: #374151;
}

.modal-body {
  padding: 24px;
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* Status indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-indicator.draft::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #d1d5db;
}

.status-indicator.active::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #10b981;
}

.status-indicator.archived::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #6b7280;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .sidebar-container {
    background: #1f2937;
    color: #f9fafb;
  }

  .sidebar-header {
    background: #111827;
    border-bottom-color: #374151;
  }

  .sidebar-header h2 {
    color: #f9fafb;
  }

  .sidebar-tabs {
    background: #111827;
    border-bottom-color: #374151;
  }

  .tab-button {
    color: #9ca3af;
  }

  .tab-button.active {
    background: #1f2937;
    color: #3b82f6;
  }

  .tab-button:hover:not(.active) {
    background: #374151;
    color: #f3f4f6;
  }

  .cc-input {
    background: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }

  .cc-input:focus {
    border-color: #3b82f6;
  }

  .cc-button.secondary {
    background: #4b5563;
    color: #f9fafb;
  }

  .cc-button.secondary:hover {
    background: #374151;
  }

  .project-item:hover,
  .persona-item:hover,
  .artifact-item:hover {
    background-color: #111827 !important;
  }

  .project-title,
  .persona-title,
  .artifact-title {
    color: #f9fafb;
  }

  .project-description,
  .persona-description,
  .artifact-description {
    color: #d1d5db;
  }

  .export-section h3 {
    color: #f9fafb;
  }

  .modal {
    background: #1f2937;
    color: #f9fafb;
  }

  .modal-header,
  .modal-footer {
    border-color: #374151;
  }

  .modal-header h3 {
    color: #f9fafb;
  }
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .sidebar-container {
    width: 100vw;
  }

  .tab-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .tab-actions .cc-button {
    width: 100%;
    justify-content: center;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .project-item,
  .persona-item,
  .artifact-item {
    transition: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .sidebar-container {
    border-left: 2px solid #000;
  }

  .cc-input,
  .cc-button {
    border-width: 2px;
  }
}

/* Modal Systems */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  backdrop-filter: blur(2px);
}

.modal-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.2s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 32px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #111827;
}

.modal-close {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #e5e7eb;
  color: #374151;
}

.modal-body {
  padding: 32px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px 32px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

/* Form Styling */
.modal-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.form-group.checkbox {
  flex-direction: row;
  align-items: center;
  gap: 8px;
}

.form-group.checkbox label {
  margin: 0;
  font-weight: normal;
  cursor: pointer;
}

.form-help {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

.range-value {
  font-size: 12px;
  color: #6b7280;
  text-align: center;
  margin-top: 4px;
}

.tags-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 8px;
}

.tag-chip {
  display: inline-flex;
  align-items: center;
  background: #e0e7ff;
  color: #4338ca;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.tag-chip .remove-tag {
  margin-left: 6px;
  cursor: pointer;
  opacity: 0.7;
}

.tag-chip .remove-tag:hover {
  opacity: 1;
}

/* Notifications and Real-time Updates */
.notifications-container {
position: fixed;
top: 0;
left: 0;
right: 0;
bottom: 0;
pointer-events: none;
z-index: 10001;
}

/* Toast Notifications */
.toast-container {
position: fixed;
top: 20px;
right: 20px;
z-index: 10002;
max-width: 400px;
pointer-events: auto;
}

.toast {
display: flex;
align-items: center;
gap: 12px;
padding: 16px 20px;
background: white;
border-radius: 8px;
box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
border-left: 4px solid #3b82f6;
margin-bottom: 12px;
animation: toastSlideIn 0.3s ease;
pointer-events: auto;
}

.toast.success {
border-left-color: #10b981;
background: #f0fdf4;
}

.toast.error {
border-left-color: #ef4444;
background: #fef2f2;
}

.toast.warning {
border-left-color: #f59e0b;
background: #fffbeb;
}

.toast.info {
border-left-color: #3b82f6;
background: #eff6ff;
}

.toast-icon {
flex-shrink: 0;
width: 20px;
height: 20px;
}

.toast-content {
flex: 1;
font-size: 14px;
color: #374151;
}

.toast-close {
background: none;
border: none;
color: #9ca3af;
cursor: pointer;
padding: 4px;
border-radius: 4px;
transition: all 0.2s ease;
}

.toast-close:hover {
color: #6b7280;
background: rgba(0, 0, 0, 0.1);
}

@keyframes toastSlideIn {
from {
  transform: translateX(100%);
  opacity: 0;
}
to {
  transform: translateX(0);
  opacity: 1;
}
}

.toast.fade-out {
animation: toastSlideOut 0.3s ease forwards;
}

@keyframes toastSlideOut {
from {
  transform: translateX(0);
  opacity: 1;
}
to {
  transform: translateX(100%);
  opacity: 0;
}
}

/* Progress Indicators */
.progress-container {
position: fixed;
top: 0;
left: 0;
right: 0;
bottom: 0;
background: rgba(0, 0, 0, 0.5);
backdrop-filter: blur(2px);
z-index: 10001;
pointer-events: auto;
display: flex;
align-items: center;
justify-content: center;
}

.progress-overlay {
background: white;
border-radius: 12px;
padding: 32px;
max-width: 400px;
width: 90%;
text-align: center;
box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.progress-spinner {
width: 40px;
height: 40px;
border: 4px solid #e5e7eb;
border-top: 4px solid #3b82f6;
border-radius: 50%;
animation: spin 1s linear infinite;
margin: 0 auto 16px;
}

.progress-text {
font-size: 16px;
font-weight: 500;
color: #374151;
margin-bottom: 16px;
}

.progress-bar {
background: #e5e7eb;
border-radius: 8px;
height: 8px;
overflow: hidden;
margin-bottom: 16px;
}

.progress-fill {
height: 100%;
background: linear-gradient(90deg, #3b82f6, #1d4ed8);
transition: width 0.3s ease;
border-radius: 8px;
}

.progress-cancel {
background: #ef4444;
color: white;
border: none;
padding: 8px 16px;
border-radius: 6px;
font-size: 14px;
cursor: pointer;
transition: all 0.2s ease;
}

.progress-cancel:hover {
background: #dc2626;
}

/* Undo/Redo Bar */
.undo-redo-bar {
position: fixed;
bottom: 20px;
left: 50%;
transform: translateX(-50%);
background: white;
border-radius: 8px;
box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
z-index: 10002;
pointer-events: auto;
animation: undoRedoSlideUp 0.3s ease;
}

.undo-redo-content {
display: flex;
align-items: center;
gap: 16px;
padding: 12px 20px;
}

.undo-redo-message {
font-size: 14px;
color: #374151;
flex: 1;
text-align: center;
}

.undo-btn,
.redo-btn {
display: flex;
align-items: center;
gap: 6px;
padding: 8px 12px;
background: white;
border: 1px solid #d1d5db;
border-radius: 6px;
color: #374151;
font-size: 14px;
cursor: pointer;
transition: all 0.2s ease;
}

.undo-btn:hover:not(:disabled),
.redo-btn:hover:not(:disabled) {
background: #f9fafb;
border-color: #9ca3af;
}

.undo-btn:disabled,
.redo-btn:disabled {
opacity: 0.5;
cursor: not-allowed;
}

@keyframes undoRedoSlideUp {
from {
  transform: translateX(-50%) translateY(100%);
  opacity: 0;
}
to {
  transform: translateX(-50%) translateY(0);
  opacity: 1;
}
}

.undo-redo-bar.hide {
animation: undoRedoSlideDown 0.3s ease forwards;
}

@keyframes undoRedoSlideDown {
from {
  transform: translateX(-50%) translateY(0);
  opacity: 1;
}
to {
  transform: translateX(-50%) translateY(100%);
  opacity: 0;
}
}

/* Loading States for Components */
.loading-shimmer {
background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
background-size: 200% 100%;
animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
0% {
  background-position: -200% 0;
}
100% {
  background-position: 200% 0;
}
}

.list-item-loading {
height: 60px;
border-radius: 8px;
margin-bottom: 12px;
}

/* Real-time Update Indicators */
.update-indicator {
position: relative;
overflow: hidden;
}

.update-indicator::after {
content: '';
position: absolute;
top: 0;
left: -100%;
width: 100%;
height: 100%;
background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
animation: updateFlash 1s ease;
}

@keyframes updateFlash {
0% {
  left: -100%;
}
100% {
  left: 100%;
}
}

/* Status Updates */
.status-update {
display: flex;
align-items: center;
gap: 8px;
padding: 8px 12px;
background: #f0f9ff;
border: 1px solid #3b82f6;
border-radius: 6px;
font-size: 14px;
color: #1e40af;
margin: 8px 0;
animation: statusUpdateSlideIn 0.3s ease;
}

.status-update.success {
background: #f0fdf4;
border-color: #10b981;
color: #065f46;
}

.status-update.error {
background: #fef2f2;
border-color: #ef4444;
color: #991b1b;
}

@keyframes statusUpdateSlideIn {
from {
  transform: translateY(-10px);
  opacity: 0;
}
to {
  transform: translateY(0);
  opacity: 1;
}
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
.toast {
  background: #1f2937;
  color: #f9fafb;
}

.toast.success {
  background: #064e3b;
}

.toast.error {
  background: #7f1d1d;
}

.toast.warning {
  background: #78350f;
}

.toast.info {
  background: #1e3a8a;
}

.progress-overlay {
  background: #1f2937;
  color: #f9fafb;
}

.undo-redo-bar {
  background: #1f2937;
  color: #f9fafb;
}

.status-update {
  background: #1e3a8a;
  color: #dbeafe;
}
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
.toast,
.undo-redo-bar,
.status-update,
.update-indicator::after {
  animation: none;
}

.progress-spinner {
  animation: none;
}
}

/* Export Preview */
.export-preview {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-top: 24px;
}

.export-preview h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.preview-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.preview-label {
  color: #6b7280;
}

.preview-value {
  font-weight: 500;
  color: #111827;
}

/* Form Options */
.form-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px 0;
  border-top: 1px solid #e5e7eb;
  border-bottom: 1px solid #e5e7eb;
  margin: 24px 0;
}

/* Dark Mode for Modals */
@media (prefers-color-scheme: dark) {
  .modal-container {
    background: #1f2937;
    color: #f9fafb;
  }

  .modal-header {
    background: #111827;
    border-bottom-color: #374151;
  }

  .modal-header h3 {
    color: #f9fafb;
  }

  .modal-footer {
    background: #111827;
    border-top-color: #374151;
  }

  .form-group label {
    color: #d1d5db;
  }

  .form-help {
    color: #9ca3af;
  }

  .export-preview {
    background: #111827;
    border-color: #374151;
  }

  .export-preview h4 {
    color: #f9fafb;
  }

  .form-options {
    border-color: #374151;
  }
}

/* Responsive Modal Adjustments */
@media (max-width: 640px) {
  .modal-overlay {
    padding: 10px;
  }

  .modal-container {
    max-width: 100%;
    max-height: calc(100vh - 20px);
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 20px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

/* Loading States */
.modal-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 12px;
  padding: 40px 20px;
  color: #6b7280;
}

.modal-loading svg {
  width: 32px;
  height: 32px;
}

/* Success/Error States */
.modal-success,
.modal-error {
  text-align: center;
  padding: 40px 20px;
}

.modal-success {
  color: #059669;
}

.modal-error {
  color: #dc2626;
}

.modal-success svg,
.modal-error svg {
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
}

/* Panel-Based Layout Styles */
.panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
  flex-shrink: 0;
  flex-wrap: wrap;
  gap: 12px;
}

.panel-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.panel-footer {
  padding: 12px 20px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  flex-shrink: 0;
}

.section-header {
  padding: 16px 20px 8px 20px;
  margin: 0;
}

.section-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Stats Bar */
.stats-bar {
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-label {
  font-size: 11px;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 2px;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #3b82f6;
}

/* Active Persona Display */
.active-persona-section {
  margin-bottom: 24px;
}

.active-persona-display {
  padding: 0 20px;
  margin-bottom: 16px;
}

.no-active-persona {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 24px 16px;
  text-align: center;
  color: #6b7280;
}

.active-persona-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #f0f9ff;
  border: 1px solid #3b82f6;
  border-radius: 8px;
  margin: 0 20px 16px 20px;
}

.active-persona-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #3b82f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.active-persona-info {
  flex: 1;
}

.active-persona-name {
  font-weight: 600;
  color: #1e40af;
  font-size: 14px;
}

.active-persona-role {
  font-size: 12px;
  color: #1e40af;
  opacity: 0.8;
}

/* Project Filter */
.project-filter-section {
  margin-bottom: 24px;
}

.project-filter {
  padding: 0 20px;
  margin-bottom: 16px;
}

/* Export Actions Grid */
.export-actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  padding: 0 20px;
  margin-bottom: 24px;
}

.export-actions-grid .cc-button {
  justify-content: flex-start;
  padding: 12px;
  min-height: 44px;
}

/* Export Templates */
.export-templates {
  padding: 0 20px;
}

.template-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin-bottom: 8px;
  background: white;
  transition: all 0.2s ease;
}

.template-item:hover {
  background: #f9fafb;
  border-color: #3b82f6;
}

.template-item:last-child {
  margin-bottom: 0;
}

.template-info {
  flex: 1;
}

.template-name {
  font-weight: 600;
  color: #111827;
  font-size: 14px;
  margin-bottom: 4px;
}

.template-description {
  font-size: 12px;
  color: #6b7280;
}

/* Enhanced List Items */
.enhanced-list-item {
  transition: all 0.2s ease;
}

.enhanced-list-item:hover {
  transform: translateX(2px);
}

.enhanced-list-item.active {
  background: #f0f9ff;
  border-color: #3b82f6;
}

.enhanced-list-item .item-actions {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.enhanced-list-item:hover .item-actions {
  opacity: 1;
}

.item-meta-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 4px;
}

.item-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  font-size: 10px;
  font-weight: 500;
  border-radius: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.item-badge.active {
  background: #dcfce7;
  color: #166534;
}

.item-badge.draft {
  background: #fef3c7;
  color: #92400e;
}

.item-badge.archived {
  background: #f3f4f6;
  color: #374151;
}

/* Loading States */
.panel-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 12px;
  padding: 40px 20px;
  color: #6b7280;
  font-size: 14px;
}

/* Artifact Editor Styles */
.artifact-editor-component {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* Editor Header */
.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
  flex-shrink: 0;
}

.editor-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.editor-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.editor-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #6b7280;
}

.editor-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* Editor Toolbar */
.editor-toolbar {
  display: flex;
  gap: 16px;
  padding: 12px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
  flex-shrink: 0;
  flex-wrap: wrap;
}

.toolbar-group {
  display: flex;
  gap: 4px;
  align-items: center;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  font-weight: 500;
}

.toolbar-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.toolbar-btn:active {
  background: #e5e7eb;
}

/* Editor Container */
.editor-container {
  flex: 1;
  display: flex;
  overflow: hidden;
  position: relative;
}

.editor-pane,
.preview-pane {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.editor-wrapper,
.preview-wrapper {
  flex: 1;
  overflow: auto;
  padding: 20px;
}

/* Markdown Editor */
.markdown-editor {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  resize: none;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.6;
  background: white;
  color: #1f2937;
  padding: 0;
}

.markdown-editor:focus {
  outline: none;
}

/* Markdown Preview */
.markdown-preview {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: #1f2937;
}

.markdown-preview h1,
.markdown-preview h2,
.markdown-preview h3,
.markdown-preview h4,
.markdown-preview h5,
.markdown-preview h6 {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-preview h1 {
  font-size: 1.875em;
  padding-bottom: 0.3em;
  border-bottom: 1px solid #e5e7eb;
}

.markdown-preview h2 {
  font-size: 1.5em;
}

.markdown-preview h3 {
  font-size: 1.25em;
}

.markdown-preview p {
  margin: 1em 0;
}

.markdown-preview code {
  background: #f3f4f6;
  padding: 0.125em 0.25em;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875em;
}

.markdown-preview pre {
  background: #f3f4f6;
  padding: 1em;
  border-radius: 6px;
  overflow-x: auto;
  margin: 1em 0;
}

.markdown-preview pre code {
  background: none;
  padding: 0;
}

.markdown-preview blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1em;
  margin: 1em 0;
  color: #6b7280;
  font-style: italic;
}

.markdown-preview ul,
.markdown-preview ol {
  margin: 1em 0;
  padding-left: 2em;
}

.markdown-preview li {
  margin: 0.25em 0;
}

.markdown-preview a {
  color: #3b82f6;
  text-decoration: none;
}

.markdown-preview a:hover {
  text-decoration: underline;
}

/* Editor Resizer */
.editor-resizer {
  width: 8px;
  background: #f3f4f6;
  cursor: col-resize;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  flex-shrink: 0;
}

.resizer-handle {
  width: 4px;
  height: 40px;
  background: #d1d5db;
  border-radius: 2px;
}

/* Editor Footer */
.editor-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  flex-shrink: 0;
}

.editor-mode-toggle {
  display: flex;
  gap: 4px;
}

.mode-btn {
  padding: 6px 12px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  color: #374151;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.mode-btn:hover {
  background: #f3f4f6;
}

.mode-btn.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.editor-search {
  display: flex;
  gap: 4px;
  align-items: center;
}

.editor-search input {
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 12px;
  width: 150px;
}

.editor-search input:focus {
  outline: none;
  border-color: #3b82f6;
}

.search-btn {
  width: 24px;
  height: 24px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 3px;
  color: #374151;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.search-btn:hover {
  background: #f3f4f6;
}

/* Split Pane Modes */
.editor-container.edit-only .preview-pane,
.editor-container.preview-only .editor-pane {
  display: none;
}

.editor-container.edit-only .editor-resizer,
.editor-container.preview-only .editor-resizer {
  display: none;
}

.editor-container.edit-only .editor-pane,
.editor-container.preview-only .preview-pane {
  flex: 1;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .artifact-editor-component {
    background: #1f2937;
    color: #f9fafb;
  }

  .editor-header {
    background: #111827;
    border-bottom-color: #374151;
  }

  .editor-title {
    color: #f9fafb;
  }

  .editor-toolbar {
    background: #111827;
    border-bottom-color: #374151;
  }

  .toolbar-btn {
    background: #1f2937;
    border-color: #374151;
    color: #d1d5db;
  }

  .toolbar-btn:hover {
    background: #374151;
  }

  .markdown-editor {
    background: #1f2937;
    color: #f9fafb;
  }

  .markdown-preview {
    color: #f9fafb;
  }

  .editor-footer {
    background: #111827;
    border-top-color: #374151;
  }

  .mode-btn {
    background: #1f2937;
    border-color: #374151;
    color: #d1d5db;
  }

  .mode-btn:hover {
    background: #374151;
  }

  .search-btn {
    background: #1f2937;
    border-color: #374151;
    color: #d1d5db;
  }

  .search-btn:hover {
    background: #374151;
  }
}

/* Version Control Styles */
.version-control-component {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.version-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
  flex-shrink: 0;
}

.version-info h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.version-stats {
  font-size: 12px;
  color: #6b7280;
}

.version-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.version-list-container {
  flex: 1;
  overflow-y: auto;
  padding: 0 20px;
}

.version-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 20px 0;
}

.version-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.version-item:hover {
  background: #f9fafb;
  border-color: #3b82f6;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.version-item.current {
  border-color: #10b981;
  background: #f0fdf4;
}

.version-item-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.version-number {
  font-weight: 600;
  color: #111827;
  font-size: 14px;
}

.version-summary {
  font-size: 13px;
  color: #6b7280;
}

.version-meta {
  font-size: 12px;
  color: #9ca3af;
  display: flex;
  align-items: center;
  gap: 12px;
}

.version-actions {
  display: flex;
  gap: 8px;
}

.version-btn {
  padding: 6px 8px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  color: #374151;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.version-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.version-btn.primary {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.version-btn.primary:hover {
  background: #2563eb;
}

.version-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 12px;
  padding: 40px 20px;
  color: #6b7280;
  font-size: 14px;
}

/* Version Comparison */
.version-comparison {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

.comparison-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
  flex-shrink: 0;
}

.comparison-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.comparison-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.comparison-actions {
  display: flex;
  gap: 8px;
}

.diff-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.diff-view {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.diff-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 16px;
  background: #f1f5f9;
  border-bottom: 1px solid #e2e8f0;
  font-size: 14px;
  color: #374151;
  gap: 8px;
}

.diff-version {
  font-weight: 600;
  padding: 4px 8px;
  background: white;
  border-radius: 4px;
  border: 1px solid #d1d5db;
}

.diff-content {
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.diff-added {
  background: #d1fae5;
  border-left: 3px solid #10b981;
  padding: 4px 8px;
  margin: 2px 0;
  color: #065f46;
}

.diff-removed {
  background: #fee2e2;
  border-left: 3px solid #ef4444;
  padding: 4px 8px;
  margin: 2px 0;
  color: #991b1b;
}

.diff-unchanged {
  background: #f8fafc;
  padding: 4px 8px;
  margin: 2px 0;
  color: #374151;
  opacity: 0.7;
}

/* Dark Mode Support for Version Control */
@media (prefers-color-scheme: dark) {
  .version-control-component {
    background: #1f2937;
    color: #f9fafb;
  }

  .version-header {
    background: #111827;
    border-bottom-color: #374151;
  }

  .version-info h3 {
    color: #f9fafb;
  }

  .version-item {
    background: #1f2937;
    border-color: #374151;
  }

  .version-item:hover {
    background: #111827;
    border-color: #3b82f6;
  }

  .version-item.current {
    background: #064e3b;
    border-color: #10b981;
  }

  .version-number {
    color: #f9fafb;
  }

  .version-summary {
    color: #d1d5db;
  }

  .version-comparison {
    background: #1f2937;
  }

  .comparison-header {
    background: #111827;
    border-bottom-color: #374151;
  }

  .comparison-title {
    color: #f9fafb;
  }

  .diff-view {
    background: #111827;
    border-color: #374151;
  }

  .diff-header {
    background: #1f2937;
    border-bottom-color: #374151;
    color: #d1d5db;
  }

  .diff-content {
    background: #1f2937;
    color: #f9fafb;
  }

  .diff-added {
    background: #064e3b;
    color: #a7f3d0;
    border-left-color: #10b981;
  }

  .diff-removed {
    background: #7f1d1d;
    color: #fecaca;
    border-left-color: #ef4444;
  }

  .diff-unchanged {
    background: #1f2937;
    color: #9ca3af;
  }
}