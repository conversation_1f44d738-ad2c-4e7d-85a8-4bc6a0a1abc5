// extension/sidebar/script.js - Enhanced Sidebar functionality with dynamic panels

class SidebarManager {
  constructor() {
    this.currentTab = 'projects';
    this.panels = {};
    this.modules = {};
    this.panelsLoaded = false;
    this.init();
  }

  async init() {
    console.log('[Sidebar] Initializing sidebar...');

    await this.loadPanels();
    this.setupEventListeners();
    this.loadInitialData();
  }

  async loadPanels() {
    console.log('[Sidebar] Loading panel components...');

    // Instead of loading from files, create panels directly in JavaScript
    this.createProjectsPanel();
    this.createPersonasPanel();
    this.createArtifactsPanel();
    this.createExportPanel();

    this.panelsLoaded = true;
    console.log('[Sidebar] All panels loaded, panelsLoaded =', this.panelsLoaded);
  }

  createProjectsPanel() {
    const tabElement = document.getElementById('projects-tab');
    if (tabElement) {
      tabElement.innerHTML = `
        <div class="panel-header">
          <div class="panel-actions">
            <button id="new-project-btn" class="cc-button primary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="12" y1="5" x2="12" y2="19"/>
                <line x1="5" y1="12" x2="19" y2="12"/>
              </svg>
              New Project
            </button>
            <button id="refresh-projects-btn" class="cc-button secondary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="23,4 23,10 17,10"/>
                <path d="M20.49,15A9,9,0,1,1,5.64,5.64L23,10"/>
              </svg>
            </button>
          </div>
          <div class="search-container">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="11" cy="11" r="8"/>
              <path d="M21 21l-4.35-4.35"/>
            </svg>
            <input type="text" id="project-search" class="cc-input" placeholder="Search projects...">
          </div>
        </div>
        <div class="panel-content">
          <div id="projects-list" class="cc-list">
            <div class="cc-loading">
              <div class="spinner"></div>
              Loading projects...
            </div>
          </div>
        </div>
        <div class="panel-footer">
          <div class="stats-bar">
            <div class="stat-item">
              <span class="stat-label">Total:</span>
              <span class="stat-value" id="projects-total">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Active:</span>
              <span class="stat-value" id="projects-active">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Completed:</span>
              <span class="stat-value" id="projects-completed">0</span>
            </div>
          </div>
        </div>
      `;
      console.log('[Sidebar] Created projects panel');
    }
  }

  createPersonasPanel() {
    const tabElement = document.getElementById('personas-tab');
    if (tabElement) {
      tabElement.innerHTML = `
        <div class="panel-header">
          <div class="panel-actions">
            <button id="new-persona-btn" class="cc-button primary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="12" y1="5" x2="12" y2="19"/>
                <line x1="5" y1="12" x2="19" y2="12"/>
              </svg>
              New Persona
            </button>
            <button id="refresh-personas-btn" class="cc-button secondary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="23,4 23,10 17,10"/>
                <path d="M20.49,15A9,9,0,1,1,5.64,5.64L23,10"/>
              </svg>
            </button>
          </div>
          <div class="search-container">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="11" cy="11" r="8"/>
              <path d="M21 21l-4.35-4.35"/>
            </svg>
            <input type="text" id="persona-search" class="cc-input" placeholder="Search personas...">
          </div>
        </div>
        <div class="panel-content">
          <div class="active-persona-section">
            <div class="section-header">
              <h4>Active Persona</h4>
            </div>
            <div id="active-persona-display" class="active-persona-display">
              <div class="no-active-persona">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                  <circle cx="12" cy="7" r="4"/>
                </svg>
                <p>No active persona selected</p>
              </div>
            </div>
          </div>
          <div class="section-header">
            <h4>All Personas</h4>
          </div>
          <div id="personas-list" class="cc-list">
            <div class="cc-loading">
              <div class="spinner"></div>
              Loading personas...
            </div>
          </div>
        </div>
        <div class="panel-footer">
          <div class="stats-bar">
            <div class="stat-item">
              <span class="stat-label">Total:</span>
              <span class="stat-value" id="personas-total">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Active:</span>
              <span class="stat-value" id="personas-active">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Usage:</span>
              <span class="stat-value" id="personas-usage">0</span>
            </div>
          </div>
        </div>
      `;
      console.log('[Sidebar] Created personas panel');
    }
  }

  createArtifactsPanel() {
    const tabElement = document.getElementById('artifacts-tab');
    if (tabElement) {
      tabElement.innerHTML = `
        <div class="panel-header">
          <div class="panel-actions">
            <button id="new-artifact-btn" class="cc-button primary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="12" y1="5" x2="12" y2="19"/>
                <line x1="5" y1="12" x2="19" y2="12"/>
              </svg>
              New Artifact
            </button>
            <button id="refresh-artifacts-btn" class="cc-button secondary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="23,4 23,10 17,10"/>
                <path d="M20.49,15A9,9,0,1,1,5.64,5.64L23,10"/>
              </svg>
            </button>
          </div>
          <div class="search-container">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="11" cy="11" r="8"/>
              <path d="M21 21l-4.35-4.35"/>
            </svg>
            <input type="text" id="artifact-search" class="cc-input" placeholder="Search artifacts...">
          </div>
        </div>
        <div class="panel-content">
          <div class="project-filter-section">
            <div class="section-header">
              <h4>Filter by Project</h4>
            </div>
            <div class="project-filter">
              <select id="project-filter" class="cc-input">
                <option value="">All Projects</option>
              </select>
            </div>
          </div>
          <div class="section-header">
            <h4>Artifacts</h4>
          </div>
          <div id="artifacts-list" class="cc-list">
            <div class="cc-loading">
              <div class="spinner"></div>
              Loading artifacts...
            </div>
          </div>
        </div>
        <div class="panel-footer">
          <div class="stats-bar">
            <div class="stat-item">
              <span class="stat-label">Total:</span>
              <span class="stat-value" id="artifacts-total">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Words:</span>
              <span class="stat-value" id="artifacts-words">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Versions:</span>
              <span class="stat-value" id="artifacts-versions">0</span>
            </div>
          </div>
        </div>
      `;
      console.log('[Sidebar] Created artifacts panel');
    }
  }

  createExportPanel() {
    const tabElement = document.getElementById('export-tab');
    if (tabElement) {
      tabElement.innerHTML = `
        <div class="panel-header">
          <div class="panel-actions">
            <button id="refresh-export-btn" class="cc-button secondary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="23,4 23,10 17,10"/>
                <path d="M20.49,15A9,9,0,1,1,5.64,5.64L23,10"/>
              </svg>
            </button>
          </div>
        </div>
        <div class="panel-content">
          <div class="export-actions-section">
            <div class="section-header">
              <h4>Quick Export</h4>
            </div>
            <div class="export-actions-grid">
              <button id="export-current-project-btn" class="cc-button">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                  <polyline points="7,10 12,15 17,10"/>
                  <line x1="12" y1="15" x2="12" y2="3"/>
                </svg>
                Current Project
              </button>
              <button id="export-all-projects-btn" class="cc-button">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                  <polyline points="7,10 12,15 17,10"/>
                  <line x1="12" y1="15" x2="12" y2="3"/>
                </svg>
                All Projects
              </button>
              <button id="export-current-artifact-btn" class="cc-button">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                  <polyline points="14,2 14,8 20,8"/>
                </svg>
                Current Artifact
              </button>
              <button id="export-templates-btn" class="cc-button">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                  <line x1="9" y1="12" x2="15" y2="12"/>
                </svg>
                Templates
              </button>
            </div>
          </div>
          <div id="export-list" class="cc-list">
            <div class="cc-loading">
              <div class="spinner"></div>
              Loading export options...
            </div>
          </div>
        </div>
      `;
      console.log('[Sidebar] Created export panel');
    }
  }

  createFallbackPanel(panelName) {
    const tabElement = document.getElementById(`${panelName}-tab`);
    if (tabElement) {
      tabElement.innerHTML = `
        <div class="panel-header">
          <h3>${panelName.charAt(0).toUpperCase() + panelName.slice(1)}</h3>
        </div>
        <div class="panel-content">
          <div id="${panelName}-list" class="cc-list">
            <div class="cc-loading">
              <div class="spinner"></div>
              Loading ${panelName}...
            </div>
          </div>
        </div>
      `;
    }
  }

  setupEventListeners() {
    // Tab navigation
    document.querySelectorAll('.tab-button').forEach(button => {
      button.addEventListener('click', () => {
        const tabId = button.dataset.tab;
        this.switchTab(tabId);
      });
    });

    // Sidebar close button
    document.getElementById('sidebar-close').addEventListener('click', () => {
      this.closeSidebar();
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape') {
        this.closeSidebar();
      }
    });

    // Search functionality
    this.setupSearch();
  }

  switchTab(tabId) {
    console.log(`[Sidebar] Switching to tab: ${tabId}`);

    // Update tab buttons
    document.querySelectorAll('.tab-button').forEach(button => {
      button.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');

    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
      content.classList.remove('active');
    });
    document.getElementById(`${tabId}-tab`).classList.add('active');

    this.currentTab = tabId;

    // Load tab-specific data
    this.loadTabData(tabId);
  }

  async loadInitialData() {
    // Wait for panels to be loaded before loading data
    if (!this.panelsLoaded) {
      setTimeout(() => this.loadInitialData(), 100);
      return;
    }

    // Setup panel-specific event listeners
    this.setupPanelEventListeners();

    // Load data for the default tab
    await this.loadTabData(this.currentTab);
  }

  async loadTabData(tabId) {
    if (!this.panelsLoaded) {
      console.log('[Sidebar] Panels not loaded yet, waiting...');
      setTimeout(() => this.loadTabData(tabId), 100);
      return;
    }

    const listElement = document.getElementById(`${tabId}-list`);
    if (!listElement) {
      console.warn(`[Sidebar] List element not found for tab: ${tabId}`);
      return;
    }

    // Show loading state
    listElement.innerHTML = `
      <div class="cc-loading">
        <div class="spinner"></div>
        Loading ${tabId}...
      </div>
    `;

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Load mock data for now
      const mockData = this.getMockData(tabId);
      this.renderList(tabId, mockData);
      this.updateStats(tabId, mockData);

    } catch (error) {
      console.error(`[Sidebar] Failed to load ${tabId}:`, error);
      listElement.innerHTML = `
        <div class="empty-state">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10"/>
            <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/>
            <path d="M12 17h.01"/>
          </svg>
          <h3>Failed to load data</h3>
          <p>Please try again later</p>
        </div>
      `;
    }
  }

  getMockData(tabId) {
    switch (tabId) {
      case 'projects':
        return [
          {
            id: '1',
            title: 'AI Assistant Development',
            description: 'Building a comprehensive AI assistant for developers',
            status: 'active',
            lastModified: '2 hours ago',
            artifactCount: 15
          },
          {
            id: '2',
            title: 'Code Review Tool',
            description: 'Automated code review and optimization suggestions',
            status: 'draft',
            lastModified: '1 day ago',
            artifactCount: 8
          }
        ];

      case 'personas':
        return [
          {
            id: '1',
            title: 'Senior Frontend Developer',
            description: 'Expert in React, TypeScript, and modern web development',
            status: 'active',
            lastModified: '30 minutes ago'
          },
          {
            id: '2',
            title: 'Backend Architect',
            description: 'Specializes in scalable systems and API design',
            status: 'active',
            lastModified: '2 hours ago'
          }
        ];

      case 'artifacts':
        return [
          {
            id: '1',
            title: 'API Documentation',
            description: 'Comprehensive API documentation with examples',
            status: 'completed',
            lastModified: '1 hour ago',
            wordCount: 1250,
            versions: 3
          },
          {
            id: '2',
            title: 'Database Schema',
            description: 'Database design and migration scripts',
            status: 'draft',
            lastModified: '3 hours ago',
            wordCount: 450,
            versions: 1
          }
        ];

      case 'export':
        return [
          {
            id: '1',
            title: 'Current Project Export',
            description: 'Export the currently active project with all artifacts',
            status: 'ready',
            lastModified: 'Available now',
            type: 'project'
          },
          {
            id: '2',
            title: 'All Projects Archive',
            description: 'Complete backup of all projects and data',
            status: 'ready',
            lastModified: 'Available now',
            type: 'archive'
          },
          {
            id: '3',
            title: 'Personas Collection',
            description: 'Export all personas and their configurations',
            status: 'ready',
            lastModified: 'Available now',
            type: 'personas'
          },
          {
            id: '4',
            title: 'Templates Package',
            description: 'Export reusable templates and workflows',
            status: 'ready',
            lastModified: 'Available now',
            type: 'templates'
          }
        ];

      default:
        return [];
    }
  }

  renderList(tabId, items) {
    const listElement = document.getElementById(`${tabId}-list`);

    if (items.length === 0) {
      listElement.innerHTML = `
        <div class="empty-state">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5.586a1 1 0 0 1 .707.293l5.414 5.414a1 1 0 0 1 .293.707V19a2 2 0 0 1-2 2z"/>
          </svg>
          <h3>No ${tabId} found</h3>
          <p>Create your first ${tabId.slice(0, -1)} to get started</p>
        </div>
      `;
      return;
    }

    const itemsHtml = items.map(item => {
      const itemClass = `${tabId.slice(0, -1)}-item`;
      const titleClass = `${tabId.slice(0, -1)}-title`;
      const metaClass = `${tabId.slice(0, -1)}-meta`;
      const descClass = `${tabId.slice(0, -1)}-description`;

      return `
        <div class="cc-list-item ${itemClass}" data-id="${item.id}">
          <div class="${titleClass}">${item.title}</div>
          <div class="${metaClass}">
            <span class="status-indicator ${item.status}">${item.status}</span>
            <span>Last modified: ${item.lastModified}</span>
            ${item.artifactCount ? `<span>${item.artifactCount} artifacts</span>` : ''}
            ${item.wordCount ? `<span>${item.wordCount} words</span>` : ''}
          </div>
          ${item.description ? `<div class="${descClass}">${item.description}</div>` : ''}
        </div>
      `;
    }).join('');

    listElement.innerHTML = itemsHtml;

    // Add click handlers
    listElement.querySelectorAll('.cc-list-item').forEach(item => {
      item.addEventListener('click', () => {
        this.handleItemClick(tabId, item.dataset.id);
      });
    });
  }

  handleItemClick(tabId, itemId) {
    console.log(`[Sidebar] Clicked ${tabId} item: ${itemId}`);

    // Get the item data
    const mockData = this.getMockData(tabId);
    const item = mockData.find(i => i.id === itemId);

    if (!item) {
      console.warn(`[Sidebar] Item not found: ${itemId}`);
      return;
    }

    // Handle different tab actions
    switch (tabId) {
      case 'projects':
        this.handleProjectClick(item);
        break;
      case 'personas':
        this.handlePersonaClick(item);
        break;
      case 'artifacts':
        this.handleArtifactClick(item);
        break;
      case 'export':
        this.handleExportClick(item);
        break;
      default:
        console.log(`[Sidebar] No handler for tab: ${tabId}`);
    }
  }

  handleProjectClick(project) {
    console.log(`[Sidebar] Opening project: ${project.title}`);
    // TODO: Implement project opening functionality
    alert(`Opening project: ${project.title}\n\nThis functionality will be implemented soon.`);
  }

  handlePersonaClick(persona) {
    console.log(`[Sidebar] Activating persona: ${persona.title}`);
    // TODO: Implement persona activation
    alert(`Activating persona: ${persona.title}\n\nThis functionality will be implemented soon.`);
  }

  handleArtifactClick(artifact) {
    console.log(`[Sidebar] Opening artifact: ${artifact.title}`);
    // TODO: Implement artifact editor
    alert(`Opening artifact: ${artifact.title}\n\nThis functionality will be implemented soon.`);
  }

  handleExportClick(exportItem) {
    console.log(`[Sidebar] Starting export: ${exportItem.title}`);
    // TODO: Implement export functionality
    alert(`Starting export: ${exportItem.title}\n\nThis functionality will be implemented soon.`);
  }

  setupSearch() {
    const searchInputs = ['project-search', 'persona-search', 'artifact-search'];

    searchInputs.forEach(inputId => {
      const input = document.getElementById(inputId);
      if (input) {
        input.addEventListener('input', (event) => {
          const query = event.target.value.toLowerCase();
          this.handleSearch(inputId.replace('-search', 's'), query);
        });
      }
    });
  }

  handleSearch(tabId, query) {
    const items = document.querySelectorAll(`#${tabId}-list .cc-list-item`);

    items.forEach(item => {
      const title = item.querySelector(`.${tabId.slice(0, -1)}-title`)?.textContent || '';
      const description = item.querySelector(`.${tabId.slice(0, -1)}-description`)?.textContent || '';

      const matches = title.toLowerCase().includes(query) ||
                     description.toLowerCase().includes(query);

      item.style.display = matches ? 'block' : 'none';
    });
  }

  closeSidebar() {
    // Send message to content script to close sidebar
    chrome.runtime.sendMessage({ type: 'CLOSE_SIDEBAR' }, (response) => {
      if (response?.success) {
        console.log('[Sidebar] Sidebar closed');
      }
    });

    // Also send message to parent window (content script)
    window.parent.postMessage({ type: 'SIDEBAR_CLOSE' }, '*');
  }

  // Public methods for external communication
  openTab(tabId) {
    if (tabId !== this.currentTab) {
      this.switchTab(tabId);
    }
  }

  refreshData() {
    this.loadTabData(this.currentTab);
  }

  updateStats(tabId, data) {
    // Update statistics in the panel footer
    const totalElement = document.getElementById(`${tabId}-total`);
    if (totalElement) {
      totalElement.textContent = data.length;
    }

    switch (tabId) {
      case 'projects':
        const activeProjects = data.filter(p => p.status === 'active').length;
        const completedProjects = data.filter(p => p.status === 'completed').length;

        const activeElement = document.getElementById('projects-active');
        const completedElement = document.getElementById('projects-completed');

        if (activeElement) activeElement.textContent = activeProjects;
        if (completedElement) completedElement.textContent = completedProjects;
        break;

      case 'personas':
        const activePersonas = data.filter(p => p.status === 'active').length;
        const usageCount = data.reduce((sum, p) => sum + (p.usageCount || 0), 0);

        const personasActiveElement = document.getElementById('personas-active');
        const personasUsageElement = document.getElementById('personas-usage');

        if (personasActiveElement) personasActiveElement.textContent = activePersonas;
        if (personasUsageElement) personasUsageElement.textContent = usageCount;
        break;

      case 'artifacts':
        const totalWords = data.reduce((sum, a) => sum + (a.wordCount || 0), 0);
        const totalVersions = data.reduce((sum, a) => sum + (a.versions || 1), 0);

        const wordsElement = document.getElementById('artifacts-words');
        const versionsElement = document.getElementById('artifacts-versions');

        if (wordsElement) wordsElement.textContent = totalWords;
        if (versionsElement) versionsElement.textContent = totalVersions;
        break;
    }
  }

  setupPanelEventListeners() {
    // Setup event listeners for panel-specific buttons
    this.setupProjectsEventListeners();
    this.setupPersonasEventListeners();
    this.setupArtifactsEventListeners();
    this.setupExportEventListeners();
  }

  setupProjectsEventListeners() {
    const newProjectBtn = document.getElementById('new-project-btn');
    const refreshProjectsBtn = document.getElementById('refresh-projects-btn');

    if (newProjectBtn) {
      newProjectBtn.addEventListener('click', () => {
        console.log('[Sidebar] New project button clicked');
        alert('New Project functionality will be implemented soon.');
      });
    }

    if (refreshProjectsBtn) {
      refreshProjectsBtn.addEventListener('click', () => {
        console.log('[Sidebar] Refresh projects button clicked');
        this.loadTabData('projects');
      });
    }
  }

  setupPersonasEventListeners() {
    const newPersonaBtn = document.getElementById('new-persona-btn');
    const refreshPersonasBtn = document.getElementById('refresh-personas-btn');

    if (newPersonaBtn) {
      newPersonaBtn.addEventListener('click', () => {
        console.log('[Sidebar] New persona button clicked');
        alert('New Persona functionality will be implemented soon.');
      });
    }

    if (refreshPersonasBtn) {
      refreshPersonasBtn.addEventListener('click', () => {
        console.log('[Sidebar] Refresh personas button clicked');
        this.loadTabData('personas');
      });
    }
  }

  setupArtifactsEventListeners() {
    const newArtifactBtn = document.getElementById('new-artifact-btn');
    const refreshArtifactsBtn = document.getElementById('refresh-artifacts-btn');

    if (newArtifactBtn) {
      newArtifactBtn.addEventListener('click', () => {
        console.log('[Sidebar] New artifact button clicked');
        alert('New Artifact functionality will be implemented soon.');
      });
    }

    if (refreshArtifactsBtn) {
      refreshArtifactsBtn.addEventListener('click', () => {
        console.log('[Sidebar] Refresh artifacts button clicked');
        this.loadTabData('artifacts');
      });
    }
  }

  setupExportEventListeners() {
    const exportButtons = [
      'export-current-project-btn',
      'export-all-projects-btn',
      'export-current-artifact-btn',
      'export-templates-btn'
    ];

    exportButtons.forEach(buttonId => {
      const button = document.getElementById(buttonId);
      if (button) {
        button.addEventListener('click', () => {
          const action = buttonId.replace('export-', '').replace('-btn', '');
          console.log(`[Sidebar] Export ${action} button clicked`);
          alert(`Export ${action} functionality will be implemented soon.`);
        });
      }
    });
  }
}

// Initialize sidebar when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.sidebarManager = new SidebarManager();
});

// Listen for messages from content script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'SIDEBAR_ACTION') {
    switch (message.action) {
      case 'refresh':
        if (window.sidebarManager) {
          window.sidebarManager.refreshData();
        }
        break;
      case 'openTab':
        if (window.sidebarManager && message.tabId) {
          window.sidebarManager.openTab(message.tabId);
        }
        break;
    }
    sendResponse({ success: true });
  }
});

console.log('[Sidebar] Script loaded');