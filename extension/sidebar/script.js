// extension/sidebar/script.js - Enhanced Sidebar functionality with dynamic panels

class SidebarManager {
  constructor() {
    this.currentTab = 'projects';
    this.panels = {};
    this.modules = {};
    this.panelsLoaded = false;
    this.init();
  }

  async init() {
    console.log('[Sidebar] Initializing sidebar...');

    await this.loadPanels();
    this.setupEventListeners();
    this.loadInitialData();
  }

  async loadPanels() {
    console.log('[Sidebar] Loading panel components...');

    const panelNames = ['projects', 'personas', 'artifacts', 'export'];

    for (const panelName of panelNames) {
      try {
        const response = await fetch(chrome.runtime.getURL(`sidebar/components/${panelName}-panel.html`));
        const html = await response.text();

        // Extract the panel content (remove DOCTYPE and html wrapper if present)
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;
        const panelContent = tempDiv.querySelector(`#${panelName}-panel`) || tempDiv.firstElementChild;

        if (panelContent) {
          const tabElement = document.getElementById(`${panelName}-tab`);
          if (tabElement) {
            tabElement.innerHTML = panelContent.innerHTML;
            console.log(`[Sidebar] Loaded ${panelName} panel`);
          }
        }
      } catch (error) {
        console.error(`[Sidebar] Failed to load ${panelName} panel:`, error);
        // Fallback to basic content
        this.createFallbackPanel(panelName);
      }
    }

    this.panelsLoaded = true;
  }

  createFallbackPanel(panelName) {
    const tabElement = document.getElementById(`${panelName}-tab`);
    if (tabElement) {
      tabElement.innerHTML = `
        <div class="panel-header">
          <h3>${panelName.charAt(0).toUpperCase() + panelName.slice(1)}</h3>
        </div>
        <div class="panel-content">
          <div id="${panelName}-list" class="cc-list">
            <div class="cc-loading">
              <div class="spinner"></div>
              Loading ${panelName}...
            </div>
          </div>
        </div>
      `;
    }
  }

  setupEventListeners() {
    // Tab navigation
    document.querySelectorAll('.tab-button').forEach(button => {
      button.addEventListener('click', () => {
        const tabId = button.dataset.tab;
        this.switchTab(tabId);
      });
    });

    // Sidebar close button
    document.getElementById('sidebar-close').addEventListener('click', () => {
      this.closeSidebar();
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape') {
        this.closeSidebar();
      }
    });

    // Search functionality
    this.setupSearch();
  }

  switchTab(tabId) {
    console.log(`[Sidebar] Switching to tab: ${tabId}`);

    // Update tab buttons
    document.querySelectorAll('.tab-button').forEach(button => {
      button.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');

    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
      content.classList.remove('active');
    });
    document.getElementById(`${tabId}-tab`).classList.add('active');

    this.currentTab = tabId;

    // Load tab-specific data
    this.loadTabData(tabId);
  }

  async loadInitialData() {
    // Wait for panels to be loaded before loading data
    if (!this.panelsLoaded) {
      setTimeout(() => this.loadInitialData(), 100);
      return;
    }

    // Setup panel-specific event listeners
    this.setupPanelEventListeners();

    // Load data for the default tab
    await this.loadTabData(this.currentTab);
  }

  async loadTabData(tabId) {
    if (!this.panelsLoaded) {
      console.log('[Sidebar] Panels not loaded yet, waiting...');
      setTimeout(() => this.loadTabData(tabId), 100);
      return;
    }

    const listElement = document.getElementById(`${tabId}-list`);
    if (!listElement) {
      console.warn(`[Sidebar] List element not found for tab: ${tabId}`);
      return;
    }

    // Show loading state
    listElement.innerHTML = `
      <div class="cc-loading">
        <div class="spinner"></div>
        Loading ${tabId}...
      </div>
    `;

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Load mock data for now
      const mockData = this.getMockData(tabId);
      this.renderList(tabId, mockData);
      this.updateStats(tabId, mockData);

    } catch (error) {
      console.error(`[Sidebar] Failed to load ${tabId}:`, error);
      listElement.innerHTML = `
        <div class="empty-state">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10"/>
            <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/>
            <path d="M12 17h.01"/>
          </svg>
          <h3>Failed to load data</h3>
          <p>Please try again later</p>
        </div>
      `;
    }
  }

  getMockData(tabId) {
    switch (tabId) {
      case 'projects':
        return [
          {
            id: '1',
            title: 'AI Assistant Development',
            description: 'Building a comprehensive AI assistant for developers',
            status: 'active',
            lastModified: '2 hours ago',
            artifactCount: 15
          },
          {
            id: '2',
            title: 'Code Review Tool',
            description: 'Automated code review and optimization suggestions',
            status: 'draft',
            lastModified: '1 day ago',
            artifactCount: 8
          }
        ];

      case 'personas':
        return [
          {
            id: '1',
            title: 'Senior Frontend Developer',
            description: 'Expert in React, TypeScript, and modern web development',
            status: 'active',
            lastModified: '30 minutes ago'
          },
          {
            id: '2',
            title: 'Backend Architect',
            description: 'Specializes in scalable systems and API design',
            status: 'active',
            lastModified: '2 hours ago'
          }
        ];

      case 'artifacts':
        return [
          {
            id: '1',
            title: 'API Documentation',
            description: 'Comprehensive API documentation with examples',
            status: 'completed',
            lastModified: '1 hour ago',
            wordCount: 1250,
            versions: 3
          },
          {
            id: '2',
            title: 'Database Schema',
            description: 'Database design and migration scripts',
            status: 'draft',
            lastModified: '3 hours ago',
            wordCount: 450,
            versions: 1
          }
        ];

      case 'export':
        return [
          {
            id: '1',
            title: 'Current Project Export',
            description: 'Export the currently active project with all artifacts',
            status: 'ready',
            lastModified: 'Available now',
            type: 'project'
          },
          {
            id: '2',
            title: 'All Projects Archive',
            description: 'Complete backup of all projects and data',
            status: 'ready',
            lastModified: 'Available now',
            type: 'archive'
          },
          {
            id: '3',
            title: 'Personas Collection',
            description: 'Export all personas and their configurations',
            status: 'ready',
            lastModified: 'Available now',
            type: 'personas'
          },
          {
            id: '4',
            title: 'Templates Package',
            description: 'Export reusable templates and workflows',
            status: 'ready',
            lastModified: 'Available now',
            type: 'templates'
          }
        ];

      default:
        return [];
    }
  }

  renderList(tabId, items) {
    const listElement = document.getElementById(`${tabId}-list`);

    if (items.length === 0) {
      listElement.innerHTML = `
        <div class="empty-state">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5.586a1 1 0 0 1 .707.293l5.414 5.414a1 1 0 0 1 .293.707V19a2 2 0 0 1-2 2z"/>
          </svg>
          <h3>No ${tabId} found</h3>
          <p>Create your first ${tabId.slice(0, -1)} to get started</p>
        </div>
      `;
      return;
    }

    const itemsHtml = items.map(item => {
      const itemClass = `${tabId.slice(0, -1)}-item`;
      const titleClass = `${tabId.slice(0, -1)}-title`;
      const metaClass = `${tabId.slice(0, -1)}-meta`;
      const descClass = `${tabId.slice(0, -1)}-description`;

      return `
        <div class="cc-list-item ${itemClass}" data-id="${item.id}">
          <div class="${titleClass}">${item.title}</div>
          <div class="${metaClass}">
            <span class="status-indicator ${item.status}">${item.status}</span>
            <span>Last modified: ${item.lastModified}</span>
            ${item.artifactCount ? `<span>${item.artifactCount} artifacts</span>` : ''}
            ${item.wordCount ? `<span>${item.wordCount} words</span>` : ''}
          </div>
          ${item.description ? `<div class="${descClass}">${item.description}</div>` : ''}
        </div>
      `;
    }).join('');

    listElement.innerHTML = itemsHtml;

    // Add click handlers
    listElement.querySelectorAll('.cc-list-item').forEach(item => {
      item.addEventListener('click', () => {
        this.handleItemClick(tabId, item.dataset.id);
      });
    });
  }

  handleItemClick(tabId, itemId) {
    console.log(`[Sidebar] Clicked ${tabId} item: ${itemId}`);

    // Get the item data
    const mockData = this.getMockData(tabId);
    const item = mockData.find(i => i.id === itemId);

    if (!item) {
      console.warn(`[Sidebar] Item not found: ${itemId}`);
      return;
    }

    // Handle different tab actions
    switch (tabId) {
      case 'projects':
        this.handleProjectClick(item);
        break;
      case 'personas':
        this.handlePersonaClick(item);
        break;
      case 'artifacts':
        this.handleArtifactClick(item);
        break;
      case 'export':
        this.handleExportClick(item);
        break;
      default:
        console.log(`[Sidebar] No handler for tab: ${tabId}`);
    }
  }

  handleProjectClick(project) {
    console.log(`[Sidebar] Opening project: ${project.title}`);
    // TODO: Implement project opening functionality
    alert(`Opening project: ${project.title}\n\nThis functionality will be implemented soon.`);
  }

  handlePersonaClick(persona) {
    console.log(`[Sidebar] Activating persona: ${persona.title}`);
    // TODO: Implement persona activation
    alert(`Activating persona: ${persona.title}\n\nThis functionality will be implemented soon.`);
  }

  handleArtifactClick(artifact) {
    console.log(`[Sidebar] Opening artifact: ${artifact.title}`);
    // TODO: Implement artifact editor
    alert(`Opening artifact: ${artifact.title}\n\nThis functionality will be implemented soon.`);
  }

  handleExportClick(exportItem) {
    console.log(`[Sidebar] Starting export: ${exportItem.title}`);
    // TODO: Implement export functionality
    alert(`Starting export: ${exportItem.title}\n\nThis functionality will be implemented soon.`);
  }

  setupSearch() {
    const searchInputs = ['project-search', 'persona-search', 'artifact-search'];

    searchInputs.forEach(inputId => {
      const input = document.getElementById(inputId);
      if (input) {
        input.addEventListener('input', (event) => {
          const query = event.target.value.toLowerCase();
          this.handleSearch(inputId.replace('-search', 's'), query);
        });
      }
    });
  }

  handleSearch(tabId, query) {
    const items = document.querySelectorAll(`#${tabId}-list .cc-list-item`);

    items.forEach(item => {
      const title = item.querySelector(`.${tabId.slice(0, -1)}-title`)?.textContent || '';
      const description = item.querySelector(`.${tabId.slice(0, -1)}-description`)?.textContent || '';

      const matches = title.toLowerCase().includes(query) ||
                     description.toLowerCase().includes(query);

      item.style.display = matches ? 'block' : 'none';
    });
  }

  closeSidebar() {
    // Send message to content script to close sidebar
    chrome.runtime.sendMessage({ type: 'CLOSE_SIDEBAR' }, (response) => {
      if (response?.success) {
        console.log('[Sidebar] Sidebar closed');
      }
    });

    // Also send message to parent window (content script)
    window.parent.postMessage({ type: 'SIDEBAR_CLOSE' }, '*');
  }

  // Public methods for external communication
  openTab(tabId) {
    if (tabId !== this.currentTab) {
      this.switchTab(tabId);
    }
  }

  refreshData() {
    this.loadTabData(this.currentTab);
  }

  updateStats(tabId, data) {
    // Update statistics in the panel footer
    const totalElement = document.getElementById(`${tabId}-total`);
    if (totalElement) {
      totalElement.textContent = data.length;
    }

    switch (tabId) {
      case 'projects':
        const activeProjects = data.filter(p => p.status === 'active').length;
        const completedProjects = data.filter(p => p.status === 'completed').length;

        const activeElement = document.getElementById('projects-active');
        const completedElement = document.getElementById('projects-completed');

        if (activeElement) activeElement.textContent = activeProjects;
        if (completedElement) completedElement.textContent = completedProjects;
        break;

      case 'personas':
        const activePersonas = data.filter(p => p.status === 'active').length;
        const usageCount = data.reduce((sum, p) => sum + (p.usageCount || 0), 0);

        const personasActiveElement = document.getElementById('personas-active');
        const personasUsageElement = document.getElementById('personas-usage');

        if (personasActiveElement) personasActiveElement.textContent = activePersonas;
        if (personasUsageElement) personasUsageElement.textContent = usageCount;
        break;

      case 'artifacts':
        const totalWords = data.reduce((sum, a) => sum + (a.wordCount || 0), 0);
        const totalVersions = data.reduce((sum, a) => sum + (a.versions || 1), 0);

        const wordsElement = document.getElementById('artifacts-words');
        const versionsElement = document.getElementById('artifacts-versions');

        if (wordsElement) wordsElement.textContent = totalWords;
        if (versionsElement) versionsElement.textContent = totalVersions;
        break;
    }
  }

  setupPanelEventListeners() {
    // Setup event listeners for panel-specific buttons
    this.setupProjectsEventListeners();
    this.setupPersonasEventListeners();
    this.setupArtifactsEventListeners();
    this.setupExportEventListeners();
  }

  setupProjectsEventListeners() {
    const newProjectBtn = document.getElementById('new-project-btn');
    const refreshProjectsBtn = document.getElementById('refresh-projects-btn');

    if (newProjectBtn) {
      newProjectBtn.addEventListener('click', () => {
        console.log('[Sidebar] New project button clicked');
        alert('New Project functionality will be implemented soon.');
      });
    }

    if (refreshProjectsBtn) {
      refreshProjectsBtn.addEventListener('click', () => {
        console.log('[Sidebar] Refresh projects button clicked');
        this.loadTabData('projects');
      });
    }
  }

  setupPersonasEventListeners() {
    const newPersonaBtn = document.getElementById('new-persona-btn');
    const refreshPersonasBtn = document.getElementById('refresh-personas-btn');

    if (newPersonaBtn) {
      newPersonaBtn.addEventListener('click', () => {
        console.log('[Sidebar] New persona button clicked');
        alert('New Persona functionality will be implemented soon.');
      });
    }

    if (refreshPersonasBtn) {
      refreshPersonasBtn.addEventListener('click', () => {
        console.log('[Sidebar] Refresh personas button clicked');
        this.loadTabData('personas');
      });
    }
  }

  setupArtifactsEventListeners() {
    const newArtifactBtn = document.getElementById('new-artifact-btn');
    const refreshArtifactsBtn = document.getElementById('refresh-artifacts-btn');

    if (newArtifactBtn) {
      newArtifactBtn.addEventListener('click', () => {
        console.log('[Sidebar] New artifact button clicked');
        alert('New Artifact functionality will be implemented soon.');
      });
    }

    if (refreshArtifactsBtn) {
      refreshArtifactsBtn.addEventListener('click', () => {
        console.log('[Sidebar] Refresh artifacts button clicked');
        this.loadTabData('artifacts');
      });
    }
  }

  setupExportEventListeners() {
    const exportButtons = [
      'export-current-project-btn',
      'export-all-projects-btn',
      'export-current-artifact-btn',
      'export-templates-btn'
    ];

    exportButtons.forEach(buttonId => {
      const button = document.getElementById(buttonId);
      if (button) {
        button.addEventListener('click', () => {
          const action = buttonId.replace('export-', '').replace('-btn', '');
          console.log(`[Sidebar] Export ${action} button clicked`);
          alert(`Export ${action} functionality will be implemented soon.`);
        });
      }
    });
  }
}

// Initialize sidebar when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.sidebarManager = new SidebarManager();
});

// Listen for messages from content script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'SIDEBAR_ACTION') {
    switch (message.action) {
      case 'refresh':
        if (window.sidebarManager) {
          window.sidebarManager.refreshData();
        }
        break;
      case 'openTab':
        if (window.sidebarManager && message.tabId) {
          window.sidebarManager.openTab(message.tabId);
        }
        break;
    }
    sendResponse({ success: true });
  }
});

console.log('[Sidebar] Script loaded');