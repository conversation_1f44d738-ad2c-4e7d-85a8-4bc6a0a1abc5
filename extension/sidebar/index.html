<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Coder Companion Sidebar</title>
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <div id="sidebar-container" class="sidebar-container">
    <!-- Sidebar Header -->
    <header class="sidebar-header">
      <h2>Coder Companion</h2>
      <button id="sidebar-close" class="sidebar-close" aria-label="Close sidebar">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
    </header>

    <!-- Tab Navigation -->
    <nav class="sidebar-tabs">
      <button class="tab-button active" data-tab="projects">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M3 7v10a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2H5a2 2 0 0 0-2-2z"/>
          <path d="M8 5a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v0"/>
        </svg>
        Projects
      </button>
      <button class="tab-button" data-tab="personas">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
          <circle cx="12" cy="7" r="4"/>
        </svg>
        Personas
      </button>
      <button class="tab-button" data-tab="artifacts">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
          <polyline points="14,2 14,8 20,8"/>
        </svg>
        Artifacts
      </button>
      <button class="tab-button" data-tab="export">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
          <polyline points="7,10 12,15 17,10"/>
          <line x1="12" y1="15" x2="12" y2="3"/>
        </svg>
        Export
      </button>
    </nav>

    <!-- Tab Content -->
    <main class="sidebar-content">
      <!-- Projects Panel -->
      <div id="projects-tab" class="tab-content active">
        <!-- Panel content will be loaded dynamically -->
      </div>

      <!-- Personas Panel -->
      <div id="personas-tab" class="tab-content">
        <!-- Panel content will be loaded dynamically -->
      </div>

      <!-- Artifacts Panel -->
      <div id="artifacts-tab" class="tab-content">
        <!-- Panel content will be loaded dynamically -->
      </div>

      <!-- Export Panel -->
      <div id="export-tab" class="tab-content">
        <!-- Panel content will be loaded dynamically -->
      </div>

      <!-- Artifact Editor Panel -->
      <div id="artifact-editor-tab" class="tab-content">
        <!-- Artifact editor content will be loaded dynamically -->
      </div>

      <!-- Version Control Panel -->
      <div id="version-control-tab" class="tab-content">
        <!-- Version control content will be loaded dynamically -->
      </div>
    </main>
  </div>

  <!-- Modal Container (for future use) -->
  <div id="modal-container" class="modal-container" style="display: none;">
    <!-- Modals will be dynamically inserted here -->
  </div>

  <script src="../utils/storage.js"></script>
  <script src="script.js"></script>
</body>
</html>